package results

import (
	"testing"
	"time"
)

// TestBasicAggregation tests basic aggregation functionality without complex timing
func TestBasicAggregation(t *testing.T) {
	config := DefaultResultAggregatorConfig()
	config.AggregationInterval = 50 * time.Millisecond
	aggregator := NewResultAggregator(config)
	aggregator.SetGroupingStrategy(NewScenarioGroupingStrategy())

	err := aggregator.Start()
	if err != nil {
		t.Fatalf("Failed to start aggregator: %v", err)
	}
	defer func() {
		if err := aggregator.Stop(); err != nil {
			t.Logf("Error stopping aggregator: %v", err)
		}
	}()

	// Add a simple result
	result := createTestResult("worker1", "scenario1", true)
	err = aggregator.AddResult(result)
	if err != nil {
		t.Fatalf("Failed to add result: %v", err)
	}

	// Wait for processing and aggregation
	time.Sleep(200 * time.Millisecond)

	// Check that we have results
	if aggregator.GetResultCount() == 0 {
		t.Error("Expected at least 1 result in buffer")
	}

	// Check aggregated results
	allResults := aggregator.GetAllAggregatedResults()
	if len(allResults) == 0 {
		t.Error("Expected at least 1 aggregated result")
	}

	// Look for our scenario
	scenarioKey := "scenario:scenario1"
	if aggregated, exists := allResults[scenarioKey]; exists {
		if aggregated.TotalCount != 1 {
			t.Errorf("Expected total count 1, got %d", aggregated.TotalCount)
		}
		if aggregated.SuccessCount != 1 {
			t.Errorf("Expected success count 1, got %d", aggregated.SuccessCount)
		}
		if aggregated.SuccessRate != 1.0 {
			t.Errorf("Expected success rate 1.0, got %f", aggregated.SuccessRate)
		}
	} else {
		t.Error("Expected scenario1 aggregated result not found")
	}
}

// TestManualAggregation tests aggregation without goroutines
func TestManualAggregation(t *testing.T) {
	config := DefaultResultAggregatorConfig()
	aggregator := NewResultAggregator(config)
	aggregator.SetGroupingStrategy(NewScenarioGroupingStrategy())

	// Manually add results to buffer
	result1 := createTestResult("worker1", "scenario1", true)
	result2 := createTestResult("worker1", "scenario1", false)
	
	aggregator.addResultToBuffer(result1)
	aggregator.addResultToBuffer(result2)

	// Manually trigger aggregation
	aggregator.aggregateResults()

	// Check aggregated results
	allResults := aggregator.GetAllAggregatedResults()
	if len(allResults) != 1 {
		t.Errorf("Expected 1 aggregated group, got %d", len(allResults))
	}

	scenarioKey := "scenario:scenario1"
	if aggregated, exists := allResults[scenarioKey]; exists {
		if aggregated.TotalCount != 2 {
			t.Errorf("Expected total count 2, got %d", aggregated.TotalCount)
		}
		if aggregated.SuccessCount != 1 {
			t.Errorf("Expected success count 1, got %d", aggregated.SuccessCount)
		}
		if aggregated.FailureCount != 1 {
			t.Errorf("Expected failure count 1, got %d", aggregated.FailureCount)
		}
		if aggregated.SuccessRate != 0.5 {
			t.Errorf("Expected success rate 0.5, got %f", aggregated.SuccessRate)
		}
	} else {
		t.Error("Expected scenario1 aggregated result not found")
	}
}

// TestCalculateAggregation tests the aggregation calculation directly
func TestCalculateAggregation(t *testing.T) {
	config := DefaultResultAggregatorConfig()
	aggregator := NewResultAggregator(config)
	aggregator.SetGroupingStrategy(NewScenarioGroupingStrategy())

	// Create test results with known values
	results := []TestResult{
		createTestResultWithDuration("worker1", "scenario1", true, 100*time.Millisecond),
		createTestResultWithDuration("worker1", "scenario1", true, 200*time.Millisecond),
		createTestResultWithDuration("worker1", "scenario1", false, 300*time.Millisecond),
	}

	// Calculate aggregation
	aggregated := aggregator.calculateAggregation("test_group", results)

	// Verify calculations
	if aggregated.TotalCount != 3 {
		t.Errorf("Expected total count 3, got %d", aggregated.TotalCount)
	}
	if aggregated.SuccessCount != 2 {
		t.Errorf("Expected success count 2, got %d", aggregated.SuccessCount)
	}
	if aggregated.FailureCount != 1 {
		t.Errorf("Expected failure count 1, got %d", aggregated.FailureCount)
	}
	if aggregated.SuccessRate != 2.0/3.0 {
		t.Errorf("Expected success rate %f, got %f", 2.0/3.0, aggregated.SuccessRate)
	}
	if aggregated.MinDuration != 100*time.Millisecond {
		t.Errorf("Expected min duration 100ms, got %v", aggregated.MinDuration)
	}
	if aggregated.MaxDuration != 300*time.Millisecond {
		t.Errorf("Expected max duration 300ms, got %v", aggregated.MaxDuration)
	}
	if aggregated.MedianDuration != 200*time.Millisecond {
		t.Errorf("Expected median duration 200ms, got %v", aggregated.MedianDuration)
	}

	expectedAvg := (100 + 200 + 300) * time.Millisecond / 3
	if aggregated.AvgDuration != expectedAvg {
		t.Errorf("Expected avg duration %v, got %v", expectedAvg, aggregated.AvgDuration)
	}
}

// Helper function to create test result with specific duration
func createTestResultWithDuration(workerID, scenarioName string, success bool, duration time.Duration) TestResult {
	now := time.Now()
	
	result := TestResult{
		ID:           "test_result_" + workerID + "_" + scenarioName,
		WorkerID:     workerID,
		ScenarioName: scenarioName,
		RequestName:  "test_request",
		StartTime:    now,
		EndTime:      now.Add(duration),
		Duration:     duration,
		Success:      success,
		StatusCode:   200,
		ResponseSize: 1024,
		Metadata:     make(map[string]interface{}),
		Tags:         make(map[string]string),
		Timestamp:    now,
	}

	if !success {
		result.StatusCode = 500
		result.Error = "Test error"
	}

	return result
}
