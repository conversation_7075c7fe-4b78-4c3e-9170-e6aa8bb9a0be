package engine

import (
	"context"
	"testing"
	"time"

	"neuralmetergo/internal/engine"
	"neuralmetergo/internal/parser"
)

// Development-safe test configuration
func createDevelopmentTestPlan() *parser.TestPlan {
	return &parser.TestPlan{
		Version:     "1.0", // Required field
		Name:        "Development Test Plan",
		Duration:    parser.Duration{Duration: 200 * time.Millisecond}, // Very short
		RampUp:      parser.Duration{Duration: 50 * time.Millisecond},  // Very short ramp-up
		Concurrency: 1,                                                 // Minimal concurrency
		Scenarios: []parser.Scenario{
			{
				Name:   "dev_scenario",
				Weight: 1,
				Requests: []parser.Request{
					{
						Name:   "dev_request",
						Method: "GET",
						URL:    "http://httpbin.org/get",
					},
				},
			},
		},
	}
}

func createDevelopmentConfig() *engine.EngineConfig {
	return &engine.EngineConfig{
		MaxWorkers:          1,                      // Minimal workers
		WorkerQueueCapacity: 5,                      // Small queue
		MetricsInterval:     25 * time.Millisecond,  // Fast metrics
		StopTimeout:         500 * time.Millisecond, // Short timeout
		EnableDebugLogs:     false,
	}
}

// TestExecutionEngine_DevelopmentSafe tests basic functionality without timeouts
func TestExecutionEngine_DevelopmentSafe(t *testing.T) {
	testPlan := createDevelopmentTestPlan()
	config := createDevelopmentConfig()

	// Test engine creation
	execEngine := engine.NewExecutionEngine(testPlan, config)
	if execEngine == nil {
		t.Fatal("Failed to create execution engine")
	}

	// Test initialization
	err := execEngine.Initialize()
	if err != nil {
		t.Fatalf("Failed to initialize engine: %v", err)
	}

	// Test state after initialization
	state := execEngine.GetState()
	// After initialization, status should be initializing or pending
	if state.Status != engine.StatusPending && state.Status != engine.StatusInitializing {
		t.Errorf("Expected status Pending or Initializing after init, got %v", state.Status)
	}

	// Test metrics access
	metrics := execEngine.GetMetrics()
	// ExecutionMetrics is a struct, not a pointer, so it can't be nil
	// Just verify we can access it
	_ = metrics.StartTime
}

// TestExecutionEngine_QuickStartStop tests start/stop without waiting for completion
func TestExecutionEngine_QuickStartStop(t *testing.T) {
	testPlan := createDevelopmentTestPlan()
	config := createDevelopmentConfig()

	execEngine := engine.NewExecutionEngine(testPlan, config)

	// Initialize
	err := execEngine.Initialize()
	if err != nil {
		t.Fatalf("Failed to initialize engine: %v", err)
	}

	// Start in background with timeout protection
	ctx, cancel := context.WithTimeout(context.Background(), 1*time.Second)
	defer cancel()

	startDone := make(chan error, 1)
	go func() {
		startDone <- execEngine.Start()
	}()

	// Wait briefly for startup
	time.Sleep(100 * time.Millisecond)

	// Stop immediately
	stopErr := execEngine.Stop()
	if stopErr != nil {
		t.Logf("Stop returned error (acceptable): %v", stopErr)
	}

	// Wait for start to complete or timeout
	select {
	case startErr := <-startDone:
		if startErr != nil {
			t.Logf("Start completed with error (acceptable): %v", startErr)
		}
	case <-ctx.Done():
		t.Log("Start timed out (acceptable for development test)")
	}

	// Verify we can still access state
	finalState := execEngine.GetState()
	t.Logf("Final state: %v", finalState.Status)
}

// TestExecutionEngine_StateTransitions tests state management
func TestExecutionEngine_StateTransitions(t *testing.T) {
	testPlan := createDevelopmentTestPlan()
	config := createDevelopmentConfig()

	execEngine := engine.NewExecutionEngine(testPlan, config)

	// Initial state
	state := execEngine.GetState()
	if state.Status != engine.StatusPending {
		t.Errorf("Expected initial status Pending, got %v", state.Status)
	}

	// After initialization
	err := execEngine.Initialize()
	if err != nil {
		t.Fatalf("Failed to initialize: %v", err)
	}

	state = execEngine.GetState()
	if state.Status != engine.StatusPending && state.Status != engine.StatusInitializing {
		t.Errorf("Expected status Pending or Initializing after init, got %v", state.Status)
	}
}

// TestExecutionEngine_MetricsCollection tests metrics without execution
func TestExecutionEngine_MetricsCollection(t *testing.T) {
	testPlan := createDevelopmentTestPlan()
	config := createDevelopmentConfig()

	execEngine := engine.NewExecutionEngine(testPlan, config)

	// Test metrics before initialization
	metrics := execEngine.GetMetrics()
	// ExecutionMetrics is a struct, not a pointer, so it can't be nil
	_ = metrics.StartTime

	// Initialize and test metrics
	err := execEngine.Initialize()
	if err != nil {
		t.Fatalf("Failed to initialize: %v", err)
	}

	metrics = execEngine.GetMetrics()
	// Verify we can access metrics fields
	_ = metrics.StartTime

	// Verify metrics structure
	if metrics.StartTime.IsZero() {
		t.Log("Start time is zero (expected before execution)")
	}
}

// TestExecutionEngine_DevelopmentConfiguration tests configuration validation
func TestExecutionEngine_DevelopmentConfiguration(t *testing.T) {
	testPlan := createDevelopmentTestPlan()

	// Test with minimal valid configuration
	validConfig := &engine.EngineConfig{
		MaxWorkers:          1,
		WorkerQueueCapacity: 1,
		MetricsInterval:     10 * time.Millisecond,
		StopTimeout:         100 * time.Millisecond,
		EnableDebugLogs:     false,
	}

	execEngine := engine.NewExecutionEngine(testPlan, validConfig)
	err := execEngine.Initialize()
	if err != nil {
		t.Errorf("Expected no error with minimal valid config, got: %v", err)
	}
}

// TestExecutionEngine_ConcurrentAccess tests thread safety without execution
func TestExecutionEngine_ConcurrentAccess(t *testing.T) {
	testPlan := createDevelopmentTestPlan()
	config := createDevelopmentConfig()

	execEngine := engine.NewExecutionEngine(testPlan, config)

	// Initialize once
	err := execEngine.Initialize()
	if err != nil {
		t.Fatalf("Failed to initialize: %v", err)
	}

	// Test concurrent read access (safe operations only)
	done := make(chan bool, 3)

	// Concurrent state reads
	go func() {
		for i := 0; i < 10; i++ {
			_ = execEngine.GetState()
			time.Sleep(time.Millisecond)
		}
		done <- true
	}()

	// Concurrent metrics reads
	go func() {
		for i := 0; i < 10; i++ {
			_ = execEngine.GetMetrics()
			time.Sleep(time.Millisecond)
		}
		done <- true
	}()

	// Another state reader
	go func() {
		for i := 0; i < 10; i++ {
			state := execEngine.GetState()
			_ = state.Status
			time.Sleep(time.Millisecond)
		}
		done <- true
	}()

	// Wait for all goroutines
	for i := 0; i < 3; i++ {
		select {
		case <-done:
			// Success
		case <-time.After(1 * time.Second):
			t.Error("Concurrent access test timed out")
			return
		}
	}
}

// TestExecutionEngine_ErrorHandling tests error scenarios
func TestExecutionEngine_ErrorHandling(t *testing.T) {
	testPlan := createDevelopmentTestPlan()
	config := createDevelopmentConfig()

	execEngine := engine.NewExecutionEngine(testPlan, config)

	// Test starting without initialization
	err := execEngine.Start()
	if err == nil {
		t.Error("Expected error when starting without initialization")
	}

	// Test double initialization
	err = execEngine.Initialize()
	if err != nil {
		t.Fatalf("First initialization failed: %v", err)
	}

	err = execEngine.Initialize()
	if err == nil {
		t.Error("Expected error on double initialization")
	}

	// Test stop without start (should be safe)
	err = execEngine.Stop()
	if err != nil {
		t.Logf("Stop without start returned error (may be expected): %v", err)
	}
}
